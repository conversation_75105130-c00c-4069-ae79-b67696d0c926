#![cfg(feature = "test")] // для тестов

use commissioning::{set_wifi_ssid, get_wifi_ssid};


#[test]
fn test_set_and_get_wifi_ssid() {

    // Устанавливаем SSID
    let ssid = "IntegrationWiFi";
    let result = set_wifi_ssid(ssid);
    assert!(result.is_ok(), "set_wifi_ssid вернула ошибку");

    // Проверяем, что значение сохранилось
    let stored_ssid = get_wifi_ssid();
    assert_eq!(stored_ssid, Some(ssid.to_string()), "SSID не сохранился");

    // Перезаписываем SSID
    let new_ssid = "NewIntegrationWiFi";
    set_wifi_ssid(new_ssid).unwrap();
    assert_eq!(get_wifi_ssid(), Some(new_ssid.to_string()), "SSID не перезаписался");
}

// #[test]
// fn test_set_wifi_ssid_error() {
//     // Мок, который всегда возвращает ошибку
//     fn mock_error(_namespace: &str, _param_name: &str, _value: &str, _length_name: &str) -> Result<(), Box<dyn std::error::Error>> {
//         Err("Ошибка сохранения".into())
//     }

//     set_save_fn_mock(mock_error);

//     // Попытка установить SSID должна вернуть ошибку
//     let result = set_wifi_ssid("FailSSID");
//     assert!(result.is_err(), "Ожидалась ошибка при сохранении SSID");
// }
