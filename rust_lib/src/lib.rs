// Нет #![no_std] для совместимости с официальными образами Espressif

pub mod mqtt_protocol;
pub mod mqtt_protocol_ffi;
pub mod date_and_time;
pub mod date_and_time_ffi;
pub mod commissioning;
pub mod commissioning_ffi;

// это для тестов, чтобы компилятор видел все крейты
mod _esp_idf_sys_mocks;

// Реэкспорт публичных элементов
// Он работает только если подключать библиотеку в раст код, как крейт.
// Это "пробрасывает" все функции "подкрейтов" наверх, позволяя 
// обращаться к ним просто по именам, без имен крейтов.
// Конкретно сдесь есть конфликт имен в ffi и чистом раст коде.
// pub use mqtt_protocol::*;
// pub use mqtt_protocol_ffi::*;
// pub use date_and_time::*;
// pub use date_and_time_ffi::*;
// pub use commissioning::*;
// pub use commissioning_ffi::*;
