#![allow(non_camel_case_types)]

use std::ffi::{CStr, CString};
use std::os::raw::c_char;
use std::sync::Mutex;
use once_cell::sync::Lazy;
use lazy_static::lazy_static;

#[cfg(feature = "test")]
use crate::_esp_idf_sys_mocks::*;

#[cfg(not(feature = "test"))]
use esp_idf_sys::{esp_err_t, ESP_OK, ESP_ERR_INVALID_ARG, ESP_ERR_NOT_FOUND};



use crate::commissioning;



pub const DEVICE_ID_SIZE: usize = 10;
pub const GUID_SIZE: usize = 54;

type DeviceId = [u8; DEVICE_ID_SIZE];

/// Статические буферы для возврата строк в C код
static WIFI_SSID_BUFFER: Lazy<Mutex<Option<CString>>> = Lazy::new(|| Mutex::new(None));
static WIFI_PASSWORD_BUFFER: Lazy<Mutex<Option<CString>>> = Lazy::new(|| Mutex::new(None));
static MQTT_USER_ID_BUFFER: Lazy<Mutex<Option<CString>>> = Lazy::new(|| Mutex::new(None));
static MQTT_PASSWORD_BUFFER: Lazy<Mutex<Option<CString>>> = Lazy::new(|| Mutex::new(None));
static BALANCER_LINK_BUFFER: Lazy<Mutex<Option<CString>>> = Lazy::new(|| Mutex::new(None));
static GUID_BUFFER: Lazy<Mutex<Option<CString>>> = Lazy::new(|| Mutex::new(None));
lazy_static! {
    static ref DEVICE_ID_BUFFER: Mutex<Option<Box<DeviceId>>> = Mutex::new(None);
}


#[no_mangle]
pub extern "C" fn init(mac_address: *const u8) -> esp_err_t {
    if mac_address.is_null() {
        return ESP_ERR_INVALID_ARG;
    }

    // Копируем MAC адрес из указателя
    let mut mac_array = [0u8; 6];
    unsafe {
        let mac_slice = std::slice::from_raw_parts(mac_address, 6);
        mac_array.copy_from_slice(mac_slice);
    }

    match commissioning::init(mac_array) {
        Ok(()) => ESP_OK,
        Err(_) => ESP_ERR_NOT_FOUND,
    }
}



/********************************
* WiFi credentials functions
*********************************/
#[no_mangle]
pub extern "C" fn set_wifi_ssid(ssid: *const c_char) -> esp_err_t {
    if ssid.is_null() {
        return ESP_ERR_INVALID_ARG;
    }

    let cstr = unsafe { CStr::from_ptr(ssid) };
    let ssid_str = match cstr.to_str() {
        Ok(s) => s,
        Err(_) => return ESP_ERR_INVALID_ARG,
    };

    match commissioning::set_wifi_ssid(ssid_str) {
        Ok(()) => ESP_OK,
        Err(_) => ESP_ERR_NOT_FOUND,
    }
}



#[no_mangle]
pub extern "C" fn set_wifi_passw(password: *const c_char) -> esp_err_t {
    if password.is_null() {
        return ESP_ERR_INVALID_ARG;
    }

    let cstr = unsafe { CStr::from_ptr(password) };
    let password_str = match cstr.to_str() {
        Ok(s) => s,
        Err(_) => return ESP_ERR_INVALID_ARG,
    };

    match commissioning::set_wifi_password(password_str) {
        Ok(()) => ESP_OK,
        Err(_) => ESP_ERR_NOT_FOUND,
    }
}



#[no_mangle]
// pub extern "C" fn get_wifi_ssid() -> *const c_char {
//     match commissioning::get_wifi_ssid() {
//         Some(ssid) => {
//             match CString::new(ssid) {
//                 Ok(cstring) => {
//                     unsafe {
//                         WIFI_SSID_BUFFER = Some(cstring);
//                         WIFI_SSID_BUFFER.as_ref().unwrap().as_ptr()
//                     }
//                 }
//                 Err(_) => ptr::null(),
//             }
//         }
//         None => ptr::null(),
//     }
// }
pub extern "C" fn get_wifi_ssid() -> *const c_char {
    match commissioning::get_wifi_ssid() {
        Some(ssid) => match CString::new(ssid) {
            Ok(cstring) => {
                let mut buffer = WIFI_SSID_BUFFER.lock().unwrap();
                let ptr = cstring.as_ptr();
                *buffer = Some(cstring);
                ptr
            }
            Err(_) => std::ptr::null(),
        },
        None => std::ptr::null(),
    }
}



#[no_mangle]
pub extern "C" fn get_wifi_passw() -> *const c_char {
    match commissioning::get_wifi_password() {
        Some(password) => {
            match CString::new(password) {
                Ok(cstring) => {
                    let mut buffer = WIFI_PASSWORD_BUFFER.lock().unwrap();
                    let ptr = cstring.as_ptr();
                    *buffer = Some(cstring);
                    ptr
                }
                Err(_) => std::ptr::null(),
            }
        }
        None => std::ptr::null(),
    }
}



/********************************
* MQTT credentials functions
*********************************/
#[no_mangle]
pub extern "C" fn set_mqtt_user_id(user_id: *const c_char) -> esp_err_t {
    if user_id.is_null() {
        return ESP_ERR_INVALID_ARG;
    }

    let cstr = unsafe { CStr::from_ptr(user_id) };
    let user_id_str = match cstr.to_str() {
        Ok(s) => s,
        Err(_) => return ESP_ERR_INVALID_ARG,
    };

    match commissioning::set_mqtt_user_id(user_id_str) {
        Ok(()) => ESP_OK,
        Err(_) => ESP_ERR_NOT_FOUND,
    }
}



#[no_mangle]
pub extern "C" fn set_mqtt_passw(password: *const c_char) -> esp_err_t {
    if password.is_null() {
        return ESP_ERR_INVALID_ARG;
    }

    let cstr = unsafe { CStr::from_ptr(password) };
    let password_str = match cstr.to_str() {
        Ok(s) => s,
        Err(_) => return ESP_ERR_INVALID_ARG,
    };

    match commissioning::set_mqtt_password(password_str) {
        Ok(()) => ESP_OK,
        Err(_) => ESP_ERR_NOT_FOUND,
    }
}



#[no_mangle]
pub extern "C" fn get_mqtt_user_id() -> *const c_char {
    match commissioning::get_mqtt_user_id() {
        Some(user_id) => {
            match CString::new(user_id) {
                Ok(cstring) => {
                    let mut buffer = MQTT_USER_ID_BUFFER.lock().unwrap();
                    let ptr = cstring.as_ptr();
                    *buffer = Some(cstring);
                    ptr
                }
                Err(_) => std::ptr::null(),
            }
        }
        None => std::ptr::null(),
    }
}



#[no_mangle]
pub extern "C" fn get_mqtt_passw() -> *const c_char {
    match commissioning::get_mqtt_password() {
        Some(password) => {
            match CString::new(password) {
                Ok(cstring) => {
                    let mut buffer = MQTT_PASSWORD_BUFFER.lock().unwrap();
                    let ptr = cstring.as_ptr();
                    *buffer = Some(cstring);
                    ptr
                }
                Err(_) => std::ptr::null(),
            }
        }
        None => std::ptr::null(),
    }
}



/********************************
* Balancer functions
*********************************/
#[no_mangle]
pub extern "C" fn set_balancer_link(link: *const c_char) -> esp_err_t {
    if link.is_null() {
        return ESP_ERR_INVALID_ARG;
    }

    let cstr = unsafe { CStr::from_ptr(link) };
    let link_str = match cstr.to_str() {
        Ok(s) => s,
        Err(_) => return ESP_ERR_INVALID_ARG,
    };

    match commissioning::set_balancer_link(link_str) {
        Ok(()) => ESP_OK,
        Err(_) => ESP_ERR_NOT_FOUND,
    }
}



#[no_mangle]
pub extern "C" fn get_balancerlink() -> *const c_char {
    match commissioning::get_balancer_link() {
        Some(link) => {
            match CString::new(link) {
                Ok(cstring) => {
                    let mut buffer = BALANCER_LINK_BUFFER.lock().unwrap();
                    let ptr = cstring.as_ptr();
                    *buffer = Some(cstring);
                    ptr
                }
                Err(_) => std::ptr::null(),
            }
        }
        None => std::ptr::null(),
    }
}



/***************************************
*    Device ID функции из device_id.h
****************************************/
#[no_mangle]
pub extern "C" fn get_device_id() -> *const u8 {
    let device_id = commissioning::get_device_id();
    if device_id == [0u8; DEVICE_ID_SIZE] {
        std::ptr::null()
    } else {
        let mut buffer = DEVICE_ID_BUFFER.lock().unwrap();

        // кладём массив в кучу
        *buffer = Some(Box::new(device_id));

        // берём указатель на кучу (он будет жить, пока buffer не перезапишется)
        buffer.as_ref().unwrap().as_ptr()
    }
}



#[no_mangle]
pub extern "C" fn write_device_id_to_buffer(buffer: *mut u8) {
    if buffer.is_null() {
        return;
    }

    unsafe {
        let buffer_slice = std::slice::from_raw_parts_mut(buffer, DEVICE_ID_SIZE);
        commissioning::write_device_id_to_buffer(buffer_slice);
    }
}



#[no_mangle]
pub extern "C" fn write_device_id_to_buffer_as_string(buffer: *mut c_char) -> i32 {
    if buffer.is_null() {
        return 0;
    }

    unsafe {
        let buffer_slice = std::slice::from_raw_parts_mut(buffer as *mut u8, DEVICE_ID_SIZE * 2 + 1);
        commissioning::write_device_id_to_buffer_as_string(buffer_slice) as i32
    }
}



#[no_mangle]
pub extern "C" fn generate_GUID(guid_prefix: *const c_char, buffer: *mut c_char) {
    if guid_prefix.is_null() || buffer.is_null() {
        return;
    }

    let cstr = unsafe { CStr::from_ptr(guid_prefix) };
    let prefix_str = match cstr.to_str() {
        Ok(s) => s,
        Err(_) => return,
    };

    unsafe {
        let buffer_slice = std::slice::from_raw_parts_mut(buffer as *mut u8, GUID_SIZE);
        let _ = commissioning::generate_guid(prefix_str, buffer_slice);
    }
}



#[no_mangle]
pub extern "C" fn get_guid(guid_prefix: *const c_char) -> *const c_char {
    if guid_prefix.is_null() {
        return std::ptr::null();
    }

    let cstr = unsafe { CStr::from_ptr(guid_prefix) };
    let prefix_str = match cstr.to_str() {
        Ok(s) => s,
        Err(_) => return std::ptr::null(),
    };

    match commissioning::get_guid(prefix_str) {
        Ok(guid) => {
            match CString::new(guid) {
                Ok(cstring) => {
                    let mut buffer = GUID_BUFFER.lock().unwrap();
                    let ptr = cstring.as_ptr();
                    *buffer = Some(cstring);
                    ptr
                }
                Err(_) => std::ptr::null(),
            }
        }
        Err(_) => std::ptr::null(),
    }
}
