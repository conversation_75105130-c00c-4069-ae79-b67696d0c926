DOCKER_IMAGE="espressif/idf-rust:esp32s3_1.88.0.0"
echo "Используется Docker образ: $DOCKER_IMAGE"

# docker run --rm \
#     -v $PWD:/workspace \
#     -v $PWD/.cargo_cache:/opt/rust/cargo/registry \
#     -w /workspace/rust_lib \
#     -u $(id -u):$(id -g) \
#     --env CARGO_HOME=/opt/rust/cargo \
#     --env RUST_BACKTRACE=1 \
#     $DOCKER_IMAGE /bin/bash -c "
#         cargo test --target=xtensa-esp32s3-espidf -Zbuild-std=std,panic_abort --features test
#     "



# docker run --rm \
#     -v $PWD:/workspace \
#     -v $PWD/.cargo_cache:/opt/rust/cargo/registry \
#     -w /workspace/rust_lib \
#     -u $(id -u):$(id -g) \
#     --env CARGO_HOME=/opt/rust/cargo \
#     --env RUST_BACKTRACE=1 \
#     $DOCKER_IMAGE /bin/bash -c "
#         cargo test --target=x86_64-unknown-linux-gnu --features test
#     "

docker run --rm \
    -v $PWD:/workspace \
    -w /workspace/rust_lib \
    -u $(id -u):$(id -g) \
    rust:1.81.0 \
    /bin/bash -c "cargo test --features test"